{"widget_component": {"widget_id": "user_profile_card", "widget_type": "Card", "order_index": 0, "parent_id": null, "position": {"width": "match_parent", "height": "wrap_content", "margin": {"all": 16}}, "layout_properties": {"elevation": 4.0, "border_radius": 12, "background_color": "#FFFFFF", "shadow_color": "#00000029"}, "children": [{"widget_id": "card_padding_wrapper", "widget_type": "Padding", "order_index": 0, "parent_id": "user_profile_card", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"padding": {"all": 20}}, "children": [{"widget_id": "main_column", "widget_type": "Column", "order_index": 0, "parent_id": "card_padding_wrapper", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "start", "cross_axis_alignment": "stretch", "main_axis_size": "min"}, "children": [{"widget_id": "header_row", "widget_type": "Row", "order_index": 0, "parent_id": "main_column", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "space_between", "cross_axis_alignment": "center"}, "children": [{"widget_id": "user_info_column", "widget_type": "Expanded", "order_index": 0, "parent_id": "header_row", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "user_details_column", "widget_type": "Column", "order_index": 0, "parent_id": "user_info_column", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"cross_axis_alignment": "start"}, "children": [{"widget_id": "user_name", "widget_type": "Text", "order_index": 0, "parent_id": "user_details_column", "position": {"width": "wrap_content", "height": "wrap_content"}, "layout_properties": {"text": "<PERSON>", "font_size": 20, "font_weight": "bold", "color": "#212121"}, "children": []}, {"widget_id": "user_title", "widget_type": "Text", "order_index": 1, "parent_id": "user_details_column", "position": {"width": "wrap_content", "height": "wrap_content", "margin": {"top": 4}}, "layout_properties": {"text": "Senior Flutter Developer", "font_size": 14, "color": "#757575"}, "children": []}]}]}, {"widget_id": "avatar_container", "widget_type": "Container", "order_index": 1, "parent_id": "header_row", "position": {"width": 60, "height": 60}, "layout_properties": {"decoration": {"shape": "circle", "color": "#E3F2FD"}}, "children": [{"widget_id": "user_avatar", "widget_type": "Icon", "order_index": 0, "parent_id": "avatar_container", "position": {"alignment": "center"}, "layout_properties": {"icon": "person", "size": 30, "color": "#2196F3"}, "children": []}]}]}, {"widget_id": "divider_separator", "widget_type": "Divider", "order_index": 1, "parent_id": "main_column", "position": {"width": "match_parent", "height": 1, "margin": {"top": 16, "bottom": 16}}, "layout_properties": {"color": "#E0E0E0", "thickness": 1}, "children": []}, {"widget_id": "stats_row", "widget_type": "Row", "order_index": 2, "parent_id": "main_column", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "space_around", "cross_axis_alignment": "center"}, "children": [{"widget_id": "projects_stat", "widget_type": "Expanded", "order_index": 0, "parent_id": "stats_row", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "projects_column", "widget_type": "Column", "order_index": 0, "parent_id": "projects_stat", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "center", "cross_axis_alignment": "center"}, "children": [{"widget_id": "projects_count", "widget_type": "Text", "order_index": 0, "parent_id": "projects_column", "position": {"width": "wrap_content", "height": "wrap_content"}, "layout_properties": {"text": "24", "font_size": 24, "font_weight": "bold", "color": "#2196F3"}, "children": []}, {"widget_id": "projects_label", "widget_type": "Text", "order_index": 1, "parent_id": "projects_column", "position": {"width": "wrap_content", "height": "wrap_content", "margin": {"top": 4}}, "layout_properties": {"text": "Projects", "font_size": 12, "color": "#757575"}, "children": []}]}]}, {"widget_id": "experience_stat", "widget_type": "Expanded", "order_index": 1, "parent_id": "stats_row", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "experience_column", "widget_type": "Column", "order_index": 0, "parent_id": "experience_stat", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "center", "cross_axis_alignment": "center"}, "children": [{"widget_id": "experience_count", "widget_type": "Text", "order_index": 0, "parent_id": "experience_column", "position": {"width": "wrap_content", "height": "wrap_content"}, "layout_properties": {"text": "5+", "font_size": 24, "font_weight": "bold", "color": "#4CAF50"}, "children": []}, {"widget_id": "experience_label", "widget_type": "Text", "order_index": 1, "parent_id": "experience_column", "position": {"width": "wrap_content", "height": "wrap_content", "margin": {"top": 4}}, "layout_properties": {"text": "Years", "font_size": 12, "color": "#757575"}, "children": []}]}]}, {"widget_id": "rating_stat", "widget_type": "Expanded", "order_index": 2, "parent_id": "stats_row", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "rating_column", "widget_type": "Column", "order_index": 0, "parent_id": "rating_stat", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"main_axis_alignment": "center", "cross_axis_alignment": "center"}, "children": [{"widget_id": "rating_count", "widget_type": "Text", "order_index": 0, "parent_id": "rating_column", "position": {"width": "wrap_content", "height": "wrap_content"}, "layout_properties": {"text": "4.9", "font_size": 24, "font_weight": "bold", "color": "#FF9800"}, "children": []}, {"widget_id": "rating_label", "widget_type": "Text", "order_index": 1, "parent_id": "rating_column", "position": {"width": "wrap_content", "height": "wrap_content", "margin": {"top": 4}}, "layout_properties": {"text": "Rating", "font_size": 12, "color": "#757575"}, "children": []}]}]}]}, {"widget_id": "skills_section", "widget_type": "Container", "order_index": 3, "parent_id": "main_column", "position": {"width": "match_parent", "height": "wrap_content", "margin": {"top": 20}}, "layout_properties": {"padding": {"all": 0}}, "children": [{"widget_id": "skills_column", "widget_type": "Column", "order_index": 0, "parent_id": "skills_section", "position": {"width": "match_parent", "height": "wrap_content"}, "layout_properties": {"cross_axis_alignment": "start"}, "children": [{"widget_id": "skills_title", "widget_type": "Text", "order_index": 0, "parent_id": "skills_column", "position": {"width": "wrap_content", "height": "wrap_content", "margin": {"bottom": 12}}, "layout_properties": {"text": "Skills", "font_size": 16, "font_weight": "bold", "color": "#212121"}, "children": []}, {"widget_id": "skills_list", "widget_type": "ListView", "order_index": 1, "parent_id": "skills_column", "position": {"width": "match_parent", "height": 120}, "layout_properties": {"scroll_direction": "vertical", "shrink_wrap": true, "physics": "never_scrollable"}, "children": [{"widget_id": "skill_item_1", "widget_type": "Container", "order_index": 0, "parent_id": "skills_list", "position": {"width": "match_parent", "height": 40, "margin": {"bottom": 8}}, "layout_properties": {"decoration": {"color": "#E3F2FD", "border_radius": 20}}, "children": [{"widget_id": "skill_row_1", "widget_type": "Row", "order_index": 0, "parent_id": "skill_item_1", "position": {"width": "match_parent", "height": "match_parent"}, "layout_properties": {"main_axis_alignment": "space_between", "cross_axis_alignment": "center"}, "children": [{"widget_id": "skill_info_1", "widget_type": "Expanded", "order_index": 0, "parent_id": "skill_row_1", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "skill_text_1", "widget_type": "Padding", "order_index": 0, "parent_id": "skill_info_1", "position": {"width": "match_parent", "height": "match_parent"}, "layout_properties": {"padding": {"left": 16, "right": 8}}, "children": [{"widget_id": "skill_name_1", "widget_type": "Text", "order_index": 0, "parent_id": "skill_text_1", "position": {"alignment": "center_left"}, "layout_properties": {"text": "Flutter", "font_size": 14, "color": "#2196F3", "font_weight": "medium"}, "children": []}]}]}, {"widget_id": "skill_level_1", "widget_type": "Container", "order_index": 1, "parent_id": "skill_row_1", "position": {"width": 50, "height": 24, "margin": {"right": 12}}, "layout_properties": {"decoration": {"color": "#2196F3", "border_radius": 12}}, "children": [{"widget_id": "skill_percentage_1", "widget_type": "Text", "order_index": 0, "parent_id": "skill_level_1", "position": {"alignment": "center"}, "layout_properties": {"text": "95%", "font_size": 12, "color": "#FFFFFF", "font_weight": "bold"}, "children": []}]}]}]}, {"widget_id": "skill_item_2", "widget_type": "Container", "order_index": 1, "parent_id": "skills_list", "position": {"width": "match_parent", "height": 40, "margin": {"bottom": 8}}, "layout_properties": {"decoration": {"color": "#E8F5E8", "border_radius": 20}}, "children": [{"widget_id": "skill_row_2", "widget_type": "Row", "order_index": 0, "parent_id": "skill_item_2", "position": {"width": "match_parent", "height": "match_parent"}, "layout_properties": {"main_axis_alignment": "space_between", "cross_axis_alignment": "center"}, "children": [{"widget_id": "skill_info_2", "widget_type": "Expanded", "order_index": 0, "parent_id": "skill_row_2", "position": {"flex": 1}, "layout_properties": {"flex": 1}, "children": [{"widget_id": "skill_text_2", "widget_type": "Padding", "order_index": 0, "parent_id": "skill_info_2", "position": {"width": "match_parent", "height": "match_parent"}, "layout_properties": {"padding": {"left": 16, "right": 8}}, "children": [{"widget_id": "skill_name_2", "widget_type": "Text", "order_index": 0, "parent_id": "skill_text_2", "position": {"alignment": "center_left"}, "layout_properties": {"text": "Dart", "font_size": 14, "color": "#4CAF50", "font_weight": "medium"}, "children": []}]}]}, {"widget_id": "skill_level_2", "widget_type": "Container", "order_index": 1, "parent_id": "skill_row_2", "position": {"width": 50, "height": 24, "margin": {"right": 12}}, "layout_properties": {"decoration": {"color": "#4CAF50", "border_radius": 12}}, "children": [{"widget_id": "skill_percentage_2", "widget_type": "Text", "order_index": 0, "parent_id": "skill_level_2", "position": {"alignment": "center"}, "layout_properties": {"text": "90%", "font_size": 12, "color": "#FFFFFF", "font_weight": "bold"}, "children": []}]}]}]}]}]}]}]}]}]}, "widget_metadata": {"component_type": "user_profile_card", "total_widgets": 25, "max_depth": 7, "layout_patterns": ["Card -> Padding -> Column", "Row with Expanded widgets", "ListView with custom items", "Nested Column structures"]}}
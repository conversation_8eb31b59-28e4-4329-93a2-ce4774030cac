import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../models/employee_tree_model.dart';

class EmployeeDetailsPanel extends StatelessWidget {
  final EmployeeData employee;
  final VoidCallback onClose;

  const EmployeeDetailsPanel({
    super.key,
    required this.employee,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Employee Details',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              fontFamily: 'TiemposText',
              color: Colors.black,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              Icons.close,
              color: Colors.grey.shade600,
              size: 20,
            ),
            onPressed: onClose,
            tooltip: 'Close',
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEmployeeHeader(),
          const SizedBox(height: 24),
          _buildSection('Basic Information', [
            _buildInfoRow('Employee ID', employee.id),
            _buildInfoRow('Email', employee.email),
            _buildInfoRow('Phone', employee.phone ?? 'Not provided'),
            _buildInfoRow('Location', employee.location ?? 'Not provided'),
          ]),
          const SizedBox(height: 24),
          _buildSection('Position Details', [
            _buildInfoRow('Position', employee.position),
            _buildInfoRow('Department', employee.department),
            _buildInfoRow('Team', employee.team ?? 'Not assigned'),
            _buildInfoRow('Level', employee.level ?? 'Not specified'),
          ]),
          const SizedBox(height: 24),
          _buildSection('Employment Information', [
            _buildInfoRow('Join Date', _formatDate(employee.joinDate)),
            _buildInfoRow('Manager ID', employee.managerId ?? 'No manager'),
            _buildInfoRow('Years of Service', _calculateYearsOfService()),
          ]),
          const SizedBox(height: 24),
          _buildSection('Additional Details', [
            _buildInfoRow('Department Color', ''),
            _buildColorIndicator(),
          ]),
        ],
      ),
    );
  }

  Widget _buildEmployeeHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: EmployeeNode.getDepartmentColor(employee.department),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                _getInitials(employee.name),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  employee.name,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'TiemposText',
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  employee.position,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontFamily: 'TiemposText',
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: EmployeeNode.getDepartmentColor(employee.department).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    employee.department,
                    style: TextStyle(
                      fontSize: 12,
                      color: EmployeeNode.getDepartmentColor(employee.department),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: 'TiemposText',
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamily: 'TiemposText',
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontFamily: 'TiemposText',
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          SizedBox(width: 120), // Align with other rows
          const SizedBox(width: 16),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: EmployeeNode.getDepartmentColor(employee.department),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            employee.department,
            style: TextStyle(
              fontSize: 14,
              fontFamily: 'TiemposText',
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0].substring(0, (parts[0].length >= 2 ? 2 : parts[0].length)).toUpperCase();
    }
    return 'EM';
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Not provided';
    return DateFormat('MMM dd, yyyy').format(date);
  }

  String _calculateYearsOfService() {
    if (employee.joinDate == null) return 'Not available';
    
    final now = DateTime.now();
    final difference = now.difference(employee.joinDate!);
    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();
    
    if (years > 0) {
      return '$years year${years > 1 ? 's' : ''}, $months month${months > 1 ? 's' : ''}';
    } else {
      return '$months month${months > 1 ? 's' : ''}';
    }
  }
}

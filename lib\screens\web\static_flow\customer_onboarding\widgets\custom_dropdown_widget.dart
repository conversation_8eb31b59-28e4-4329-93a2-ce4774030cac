import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';

class CustomDropdownWidget extends StatelessWidget {
  CustomDropdownWidget(
      {super.key,
      required this.label,
      required this.list,
      required this.onChanged,
      required this.value,
      this.closeOnTapOutside = true,
      this.customLabelWidget,
      this.isMultiSelect = false});

  final String label;
  final List list;
  final dynamic value; // For single select: String?, For multi select: List<String>
  final Function(dynamic) onChanged;
  final bool closeOnTapOutside;
  final Widget? customLabelWidget;
  final bool isMultiSelect;

  @override
  Widget build(BuildContext context) {
    return _buildDropdown(
      context,
      label,
      list,
      value,
      onChanged,
      isMultiSelect,
    );
  }

  Widget _buildDropdown(
    BuildContext context,
    String label,
    List list,
    dynamic value,
    Function(dynamic) onChanged,
    bool isMultiSelect,
  ) {
    final layerLink = LayerLink();
    final key = GlobalKey();
    OverlayEntry? overlayEntry;

    void removeOverlay() {
      overlayEntry?.remove();
      overlayEntry = null;
    }

    void showOverlay() {
      final RenderBox renderBox =
          key.currentContext!.findRenderObject() as RenderBox;
      final Size size = renderBox.size;
      final Offset offset = renderBox.localToGlobal(Offset.zero);

      overlayEntry = OverlayEntry(
        builder: (context) => Stack(
          children: [
            if (closeOnTapOutside)
              // Fullscreen GestureDetector to detect taps outside
              Positioned.fill(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    removeOverlay();
                  },
                ),
              ),
            // The dropdown overlay itself
            Positioned(
              left: offset.dx,
              top: offset.dy + size.height,
              width: size.width,
              child: CompositedTransformFollower(
                link: layerLink,
                offset: Offset(0, size.height + 5),
                child: _buildOverlayOptions(context, list, value, isMultiSelect, (selected) {
                  onChanged(selected);
                  if (!isMultiSelect) {
                    removeOverlay();
                  }
                }),
              ),
            ),
          ],
        ),
      );

      Overlay.of(context).insert(overlayEntry!);
    }

    return CompositedTransformTarget(
      link: layerLink,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: InkWell(
          key: key,
          onTap: () {
            if (overlayEntry == null) {
              showOverlay();
            } else {
              removeOverlay();
            }
          },
          child: Container(
            height: 35,
            width: 150,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(2),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: customLabelWidget ??
                      Text(
                        _getDisplayText(value, label, isMultiSelect),
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s10,
                          fontWeight: FontWeight.w400,
                          color: _hasValue(value, isMultiSelect) ? Colors.black : Colors.grey.shade600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                ),
                Icon(Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600, size: 12),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getDisplayText(dynamic value, String label, bool isMultiSelect) {
    if (isMultiSelect) {
      final selectedList = value as List<String>? ?? [];
      if (selectedList.isEmpty) {
        return " $label";
      } else {
        return selectedList.join(', ');
      }
    } else {
      return value ?? " $label";
    }
  }

  bool _hasValue(dynamic value, bool isMultiSelect) {
    if (isMultiSelect) {
      final selectedList = value as List<String>? ?? [];
      return selectedList.isNotEmpty;
    } else {
      return value != null;
    }
  }

  bool _isItemSelected(String item, dynamic currentValue, bool isMultiSelect) {
    if (isMultiSelect) {
      final selectedList = currentValue as List<String>? ?? [];
      return selectedList.contains(item);
    } else {
      return currentValue == item;
    }
  }

  void _handleMultiSelectTap(String item, dynamic currentValue, Function(dynamic) onItemSelected) {
    final selectedList = List<String>.from(currentValue as List<String>? ?? []);

    if (selectedList.contains(item)) {
      selectedList.remove(item);
    } else {
      selectedList.add(item);
    }

    onItemSelected(selectedList);
  }

  Widget _buildOverlayOptions(
      BuildContext context, List list, dynamic currentValue, bool isMultiSelect, Function(dynamic) onItemSelected) {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        constraints: BoxConstraints(maxHeight: 200),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(2),
          color: Colors.white,
        ),
        child: ListView(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: list.map((item) {
            final isSelected = _isItemSelected(item, currentValue, isMultiSelect);

            return InkWell(
              onTap: () {
                if (isMultiSelect) {
                  _handleMultiSelectTap(item, currentValue, onItemSelected);
                } else {
                  onItemSelected(item);
                }
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue.shade50 : Colors.transparent,
                ),
                child: Row(
                  children: [
                    if (isMultiSelect)
                      Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: Icon(
                          isSelected ? Icons.check_box : Icons.check_box_outline_blank,
                          size: 16,
                          color: isSelected ? Colors.blue : Colors.grey,
                        ),
                      ),
                    Expanded(
                      child: Text(
                        item,
                        style: FontManager.getCustomStyle(
                          fontSize: FontManager.s12,
                          color: isSelected ? Colors.blue : Colors.black,
                          fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}

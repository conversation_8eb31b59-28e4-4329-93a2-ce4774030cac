import 'package:flutter/material.dart';
import 'package:nsl/theme/app_colors.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/widgets/mobile/custom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_hierarchy_mobile_provider.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_hierarchy_mobile_chart.dart';
import 'package:nsl/screens/new_design/nsl_hierarchy_mobile/nsl_node_details_bottom_panel.dart';

class NSLHierarchyMobileScreen extends StatelessWidget {
  const NSLHierarchyMobileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => NSLHierarchyMobileProvider(),
      child: const _NSLHierarchyMobileView(),
    );
  }
}

class _NSLHierarchyMobileView extends StatelessWidget {
  const _NSLHierarchyMobileView();

  @override
  Widget build(BuildContext context) {
    return Consumer<NSLHierarchyMobileProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (provider.hasError) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading hierarchy data',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                      fontFamily: 'TiemposText',
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: provider.loadNSLData,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          drawer: const CustomDrawer(),
          appBar: AppBar(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.transparent,
            foregroundColor: Colors.black,
            elevation: 0,
            automaticallyImplyLeading: false,
            titleSpacing: 0,
            title: Row(
              children: [
                // Hamburger menu icon
                Builder(
                  builder: (context) => IconButton(
                    icon: const Icon(Icons.menu, color: Colors.black, size: 24),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                ),
                 // Title or Search field
                if (!provider.showSearchField)
                  Expanded(
                    child: Text(
                      'NSL Hierarchy',
                      style: TextStyle(
                        fontSize: _getResponsiveTitleFontSize(context),
                        fontWeight: FontWeight.bold,
                        fontFamily: 'TiemposText',
                      ),
                    ),
                  ),
                if (provider.showSearchField)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8.0,top:5),
                      child: TextField(
                        controller: provider.searchController,
                        autofocus: true,
                        decoration: InputDecoration(
                          hintText: 'Search by name',
                          hintStyle:  FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: AppColors.black,
                              fontWeight: FontWeight.w500,
                            ),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          suffixIcon: IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () {
                              provider.toggleSearchField();
                              provider.clearSearch();
                            },
                          ),
                        ),
                        onChanged: (value) {
                          provider.searchNodes(value);
                        },
                      ),
                    ),
                  ),
                // Search icon
                if (!provider.showSearchField)
                  IconButton(
                    icon: const Icon(Icons.search, color: Colors.black, size: 24),
                    onPressed: provider.toggleSearchField,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
              ],
            ),
          ),
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              Column(
                children: [
                  Expanded(
                    child: provider.filteredRootNode != null
                        ? NSLHierarchyMobileChart(
                            rootNode: provider.filteredRootNode!,
                            expandedNodes: provider.expandedNodes,
                            onNodeInfoTap: provider.onNodeInfoTap,
                            onNodeCircleTap: provider.onNodeCircleTap,
                            selectedNodeId: provider.selectedNodeId,                            
                          )
                        : _buildEmptyState(provider),
                  ),
                ],
              ),
              if (provider.showBottomPanel && provider.selectedNode != null)
                NSLNodeDetailsBottomPanel(
                  nodeData: provider.selectedNode!,
                  nodeDetails: provider.nodeDetails,
                  isLoadingNodeDetails: provider.isLoadingNodeDetails,
                  nodeTransactions: provider.nodeTransactions,
                  isLoadingTransactions: provider.isLoadingTransactions,
                  dateRange: provider.systemInfo?['default_time_range'],
                  onClose: provider.hideBottomPanel,
                  onDateRangeChanged: provider.onDateRangeChanged,
                ),
            ],
          ),
        );

        // Scaffold(
        //   backgroundColor: Colors.white,
        //   body: Stack(
        //     children: [
        //       // Main content area
        //       Column(
        //         children: [
        //           // Header with search
        //           // _buildHeader(context, provider),

        //           // Hierarchy chart
        //           Expanded(
        //             child: provider.filteredRootNode != null
        //                 ? NSLHierarchyMobileChart(
        //                     rootNode: provider.filteredRootNode!,
        //                     expandedNodes: provider.expandedNodes,
        //                     onNodeInfoTap: provider.onNodeInfoTap,
        //                     onNodeCircleTap: provider.onNodeCircleTap,
        //                   )
        //                 : _buildEmptyState(provider),
        //           ),
        //         ],
        //       ),

        //       // Bottom panel for node details
        //       if (provider.showBottomPanel && provider.selectedNode != null)
        //         NSLNodeDetailsBottomPanel(
        //           nodeData: provider.selectedNode!,
        //           nodeDetails: provider.nodeDetails,
        //           isLoadingNodeDetails: provider.isLoadingNodeDetails,
        //           nodeTransactions: provider.nodeTransactions,
        //           isLoadingTransactions: provider.isLoadingTransactions,
        //           dateRange: provider.systemInfo?['default_time_range'],
        //           onClose: provider.hideBottomPanel,
        //           onDateRangeChanged: provider.onDateRangeChanged,
        //         ),
        //     ],
        //   ),
        // );
      },
    );
  }

  Widget _buildEmptyState(NSLHierarchyMobileProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            provider.searchQuery.isEmpty
                ? 'No hierarchy data available'
                : 'No nodes found for "${provider.searchQuery}"',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
              fontFamily: 'TiemposText',
            ),
            textAlign: TextAlign.center,
          ),
          if (provider.searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: provider.clearSearch,
              child: const Text(
                'Clear search',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff0058FF),
                  fontFamily: 'TiemposText',
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  double _getResponsiveTitleFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) return 16.0; // Very small phones
    if (screenWidth < 400) return 18.0; // Small phones
    return 20.0; // Medium and larger phones
  }
}

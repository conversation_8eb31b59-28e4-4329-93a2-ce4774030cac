import 'package:flutter/material.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_heirarchy_model1.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/nsl_tree_side_details_new.dart';
import 'package:nsl/screens/web/nsl_hierarchy_web/models/metric_info_model.dart';
import 'package:nsl/utils/font_manager.dart';

class NSLNodeDetailsBottomPanel extends StatefulWidget {
  final NSLHierarchyData1 nodeData;
  final NslTreeSidePanel? nodeDetails;
  final bool isLoadingNodeDetails;
  final MetricsInfo? nodeTransactions;
  final bool isLoadingTransactions;
  final Map<String, dynamic>? dateRange;
  final VoidCallback onClose;
  final Function(Map<String, dynamic>)? onDateRangeChanged;

  const NSLNodeDetailsBottomPanel({
    super.key,
    required this.nodeData,
    this.nodeDetails,
    this.isLoadingNodeDetails = false,
    this.nodeTransactions,
    this.isLoadingTransactions = false,
    this.dateRange,
    required this.onClose,
    this.onDateRangeChanged,
  });

  @override
  State<NSLNodeDetailsBottomPanel> createState() => _NSLNodeDetailsBottomPanelState();
}

class _NSLNodeDetailsBottomPanelState extends State<NSLNodeDetailsBottomPanel>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late TabController _tabController;

  double _currentPanelHeight = 0.0;
  double _minPanelHeight = 0.0;
  double _maxPanelHeight = 0.0;
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize tab controller
    _tabController = TabController(length: 3, vsync: this);

    // Initialize panel dimensions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final screenHeight = MediaQuery.of(context).size.height;
      _minPanelHeight = screenHeight * 0.3;
      _maxPanelHeight = screenHeight * 0.8;
      _currentPanelHeight = screenHeight * 0.5;
      setState(() {});
      _animationController.forward();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _hidePanel() {
    _animationController.reverse().then((_) {
      widget.onClose();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _slideAnimation,
        builder: (context, child) {
          return Stack(
            children: [
              // Background overlay
              if (_slideAnimation.value < 1.0)
                GestureDetector(
                  onTap: _hidePanel,
                  child: Container(
                    color: Colors.black.withOpacity(0.3 * (1 - _slideAnimation.value)),
                  ),
                ),

              // Bottom panel
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Transform.translate(
                  offset: Offset(0, _currentPanelHeight * _slideAnimation.value),
                  child: Container(
                    height: _currentPanelHeight,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 2,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Drag handle and header
                        _buildHeader(),
                        
                        // Tab navigation
                        _buildTabNavigation(),
                        
                        // Tab content
                        Expanded(
                          child: _buildTabContent(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return GestureDetector(
      onPanUpdate: (details) {
        if (!_isResizing) {
          setState(() {
            _isResizing = true;
          });
        }

        final newHeight = _currentPanelHeight - details.delta.dy;
        setState(() {
          _currentPanelHeight = newHeight.clamp(_minPanelHeight, _maxPanelHeight);
        });
      },
      onPanEnd: (details) {
        setState(() {
          _isResizing = false;
        });

        final velocity = details.velocity.pixelsPerSecond.dy;
        if (velocity.abs() > 500) {
          if (velocity > 0) {
            if (_currentPanelHeight < _minPanelHeight * 1.5) {
              _hidePanel();
            } else {
              setState(() {
                _currentPanelHeight = _minPanelHeight;
              });
            }
          } else {
            setState(() {
              _currentPanelHeight = _maxPanelHeight;
            });
          }
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Column(
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: _isResizing ? Colors.blue.shade400 : Colors.grey.shade400,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Header content
            Row(
              children: [
                // Node level indicator
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: NSLNode.getLevelColor(widget.nodeData.level),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      widget.nodeData.level,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // Node title and info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.nodeData.title,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ID: ${widget.nodeData.id} • Level: ${widget.nodeData.levelName}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Close button
                IconButton(
                  onPressed: _hidePanel,
                  icon: const Icon(Icons.close),
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: const Color(0xFF0058FF),
        labelStyle: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Financial'),
          Tab(text: 'Metrics'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildFinancialTab(),
        _buildMetricsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('Basic Information', [
            _buildInfoRow('Type', widget.nodeData.type),
            _buildInfoRow('Level', widget.nodeData.levelName),
            if (widget.nodeData.employeeId != null)
              _buildInfoRow('Employee ID', widget.nodeData.employeeId!),
            if (widget.nodeData.parent != null)
              _buildInfoRow('Parent', widget.nodeData.parent!),
          ]),
          
          const SizedBox(height: 24),
          
          _buildInfoSection('Summary', [
            _buildInfoRow('Total Bets', widget.nodeData.totalBets.toString()),
            _buildInfoRow('Children Count', widget.nodeData.children.length.toString()),
          ]),
        ],
      ),
    );
  }

  Widget _buildFinancialTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoSection('Financial Summary', [
            // _buildInfoRow('Revenue', '\$${_formatCurrency(widget.nodeData.financialSummary.revenue)}'),
            // _buildInfoRow('Profit', '\$${_formatCurrency(widget.nodeData.financialSummary.profit)}'),
            // _buildInfoRow('Profit Margin', '${widget.nodeData.financialSummary.profitMargin.toStringAsFixed(2)}%'),
          ]),
          
          const SizedBox(height: 24),
          
          _buildInfoSection('Bet Breakdown', [
            // _buildInfoRow('Win Bets', widget.nodeData.betBreakdown.winBets.toString()),
            // _buildInfoRow('Loss Bets', widget.nodeData.betBreakdown.lossBets.toString()),
            // _buildInfoRow('Pending Bets', widget.nodeData.betBreakdown.pendingBets.toString()),
          ]),
        ],
      ),
    );
  }

  Widget _buildMetricsTab() {
    if (widget.isLoadingTransactions) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.nodeData.metrics != null)
            _buildInfoSection('Node Metrics', [
              // _buildInfoRow('Performance Score', widget.nodeData.metrics!.performanceScore.toStringAsFixed(2)),
              // _buildInfoRow('Efficiency Rating', widget.nodeData.metrics!.efficiencyRating.toStringAsFixed(2)),
            ])
          else
            const Text('No metrics available'),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

class GreetingHelper {
  // This method should ONLY be used for displaying greetings WITHOUT marking login
  // Use getGreetingAndMarkLogin() for actual login scenarios
  static Future<String> getGreeting(
      BuildContext context, String name, String userId) async {
    final now = DateTime.now();
    final String day = _getDay(now);
    final String timePeriod = _getTimePeriod(now);

    final prefs = await SharedPreferences.getInstance();

    // Check if this user has ever logged in before (WITHOUT marking login)
    final bool isFirstTimeUser = await _isFirstTimeUser(prefs, userId);

    // Create the key based on whether it's first time user or repeat user
    final key =
        'greeting_${day}_${timePeriod}_${isFirstTimeUser ? 'first' : 'repeat'}';

    // Debug logging
    print('GreetingHelper Debug (READ-ONLY):');
    print('  User ID: $userId');
    print('  Day: $day');
    print('  Time Period: $timePeriod');
    print('  Is First Time User: $isFirstTimeUser');
    print('  Generated Key: $key');
    print('  NOTE: This call does NOT mark login - use getGreetingAndMarkLogin() for login scenarios');

    return AppLocalizations.of(context)
        .translate(key)
        .replaceAll('{name}', name);
  }

  // Method to get greeting and mark login at the same time
  static Future<String> getGreetingAndMarkLogin(
      BuildContext context, String name, String userId) async {
    try {
      final now = DateTime.now();
      final String day = _getDay(now);
      final String timePeriod = _getTimePeriod(now);

      // Use a simple fallback greeting to avoid blocking
      final String fallbackGreeting = 'Hi $name!';

      // Try to get SharedPreferences with a timeout
      SharedPreferences? prefs;
      try {
        prefs = await SharedPreferences.getInstance()
            .timeout(Duration(milliseconds: 500));
      } catch (e) {
        // If SharedPreferences takes too long, return fallback
        print('SharedPreferences timeout, using fallback greeting');
        return fallbackGreeting;
      }

      // Check if this user has ever logged in before (BEFORE marking current login)
      final bool isFirstTimeUser = await _isFirstTimeUser(prefs, userId);

      // Create the key based on whether it's first time user or repeat user
      final key = 'greeting_${day}_${timePeriod}_${isFirstTimeUser ? 'first' : 'repeat'}';

      // Debug logging BEFORE marking login
      print('GreetingHelper Debug (BEFORE marking login):');
      print('  User ID: $userId');
      print('  Day: $day');
      print('  Time Period: $timePeriod');
      print('  Is First Time User: $isFirstTimeUser');
      print('  Generated Key: $key');

      // NOW mark the login AFTER determining the greeting type
      await _markLoginSynchronously(prefs, userId, now, isFirstTimeUser);

      // Try to get the localized greeting, fallback if it fails
      try {
        final greeting = AppLocalizations.of(context)
            .translate(key)
            .replaceAll('{name}', name);
        
        // If translation is empty or same as key, use fallback
        if (greeting.isEmpty || greeting == key) {
          return fallbackGreeting;
        }
        
        return greeting;
      } catch (e) {
        print('Error getting localized greeting: $e');
        return fallbackGreeting;
      }
    } catch (e) {
      print('Error in getGreetingAndMarkLogin: $e');
      return 'Hi $name!';
    }
  }

  // Synchronous method to mark login immediately (fixes the greeting logic)
  static Future<void> _markLoginSynchronously(SharedPreferences prefs, String userId, 
      DateTime now, bool wasFirstTimeUser) async {
    try {
      // Mark that this user has logged in
      final String userKey = 'user_has_logged_in_$userId';
      await prefs.setBool(userKey, true);

      // Store last login time for this user
      final String lastLoginKey = 'user_last_login_$userId';
      await prefs.setInt(lastLoginKey, now.millisecondsSinceEpoch);

      // Track total login count for this user
      final String loginCountKey = 'user_login_count_$userId';
      final int loginCount = prefs.getInt(loginCountKey) ?? 0;
      await prefs.setInt(loginCountKey, loginCount + 1);

      print('Login marked synchronously for user $userId (${wasFirstTimeUser ? 'FIRST TIME' : 'REPEAT'}, Count: ${loginCount + 1})');
    } catch (e) {
      print('Error marking login synchronously: $e');
    }
  }

  // Background method to mark login without blocking UI
  static void _markLoginInBackground(SharedPreferences prefs, String userId, 
      DateTime now, bool isFirstTimeUser) {
    // Run in microtask to avoid blocking
    Future.microtask(() async {
      try {
        // Mark that this user has logged in
        final String userKey = 'user_has_logged_in_$userId';
        await prefs.setBool(userKey, true);

        // Store last login time for this user
        final String lastLoginKey = 'user_last_login_$userId';
        await prefs.setInt(lastLoginKey, now.millisecondsSinceEpoch);

        // Track total login count for this user
        final String loginCountKey = 'user_login_count_$userId';
        final int loginCount = prefs.getInt(loginCountKey) ?? 0;
        await prefs.setInt(loginCountKey, loginCount + 1);

        print('Background login marked for user $userId (${isFirstTimeUser ? 'FIRST TIME' : 'REPEAT'}, Count: ${loginCount + 1})');
      } catch (e) {
        print('Error marking login in background: $e');
      }
    });
  }

  // Check if this is the first time this user is logging in
  // Returns true if the user has NOT previously logged in successfully (i.e., user_has_logged_in_$userId is not true)
  static Future<bool> _isFirstTimeUser(
      SharedPreferences prefs, String userId) async {
    final String userKey = 'user_has_logged_in_$userId';
    // If the key doesn't exist or its value is false, it's a first time user.
    // If it's true, they've logged in before.
    return !(prefs.getBool(userKey) ?? false);
  }

  // Method to be called ONLY on actual login
  static Future<void> markUserLogin(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Check if this is first time user (before marking the current login)
    final bool isFirstTime = !(prefs.getBool('user_has_logged_in_$userId') ?? false);


    // Mark that this user has logged in
    final String userKey = 'user_has_logged_in_$userId';
    await prefs.setBool(userKey, true);

    // Store last login time for this user
    final String lastLoginKey = 'user_last_login_$userId';
    await prefs.setInt(lastLoginKey, now.millisecondsSinceEpoch);

    // Track total login count for this user
    final String loginCountKey = 'user_login_count_$userId';
    final int loginCount = prefs.getInt(loginCountKey) ?? 0;
    await prefs.setInt(loginCountKey, loginCount + 1);

    print(
        'GreetingHelper: User $userId logged in (${isFirstTime ? 'FIRST TIME' : 'REPEAT'}, Total logins: ${loginCount + 1})');
  }

  // Method to clear user session when user logs out (optional)
  static Future<void> clearUserSession(String userId) async {
    final prefs = await SharedPreferences.getInstance();

    // Don't clear the user's login history - we want to remember they've logged in before
    // This ensures subsequent logins show repeat messages.
    // If you need to "reset" a user for testing or privacy, use resetUserForTesting().

    print(
        'GreetingHelper: Session cleared for user $userId (login history preserved)');
  }

  static String _getDay(DateTime date) {
    switch (date.weekday) {
      case DateTime.monday:
        return "monday";
      case DateTime.tuesday:
        return "tuesday";
      case DateTime.wednesday:
        return "wednesday";
      case DateTime.thursday:
        return "thursday";
      case DateTime.friday:
        return "friday";
      case DateTime.saturday:
        return "saturday";
      case DateTime.sunday:
      default:
        return "sunday";
    }
  }

  static String _getTimePeriod(DateTime date) {
    final hour = date.hour;

    if (hour >= 4 && hour < 8) {
      return "early_morning";
    } else if (hour >= 8 && hour < 12) {
      return "morning";
    } else if (hour >= 12 && hour < 17) {
      return "afternoon";
    } else if (hour >= 17 && hour < 21) {
      return "evening";
    } else {
      return "night";
    }
  }

  // Alternative approach: Time-based session management per user
  static Future<void> markUserLoginWithTimeWindow(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Store the current login timestamp for this user
    final String timestampKey = 'user_last_login_timestamp_$userId';
    await prefs.setInt(timestampKey, now.millisecondsSinceEpoch);

    // Mark that this user has logged in at least once
    final String userKey = 'user_has_logged_in_$userId';
    await prefs.setBool(userKey, true);

    print('GreetingHelper: User $userId logged in at ${now.toString()}');
  }

  // Get greeting with time-based session detection per user
  static Future<String> getGreetingWithTimeWindow(
      BuildContext context, String name, String userId,
      {int sessionWindowMinutes = 30}) async {
    final now = DateTime.now();
    final String day = _getDay(now);
    final String timePeriod = _getTimePeriod(now);

    final prefs = await SharedPreferences.getInstance();

    // Check if this user has ever logged in AND if it's a new session
    final bool isFirstTimeUser = await _isFirstTimeUser(prefs, userId);
    final bool isNewSession =
        await _isNewSessionForUser(prefs, userId, now, sessionWindowMinutes);

    // If it's a first time user, always show first message
    // If it's existing user, check session window
    final bool showFirstMessage = isFirstTimeUser || isNewSession;

    final key =
        'greeting_${day}_${timePeriod}_${showFirstMessage ? 'first' : 'repeat'}';

    // Debug logging
    print('GreetingHelper Time-based Debug:');
    print('  User ID: $userId');
    print('  Day: $day');
    print('  Time Period: $timePeriod');
    print('  Is First Time User: $isFirstTimeUser');
    print('  Is New Session: $isNewSession');
    print('  Show First Message: $showFirstMessage');
    print('  Generated Key: $key');

    return AppLocalizations.of(context)
        .translate(key)
        .replaceAll('{name}', name);
  }

  // Check if this is a new session for specific user based on time window
  static Future<bool> _isNewSessionForUser(SharedPreferences prefs,
      String userId, DateTime now, int windowMinutes) async {
    final String timestampKey = 'user_last_login_timestamp_$userId';
    final int? lastLoginTimestamp = prefs.getInt(timestampKey);

    if (lastLoginTimestamp == null) {
      // First time ever for this user
      return true;
    }

    final DateTime lastLogin =
        DateTime.fromMillisecondsSinceEpoch(lastLoginTimestamp);
    final int minutesSinceLastLogin = now.difference(lastLogin).inMinutes;

    // If more than windowMinutes have passed, consider it a new session
    return minutesSinceLastLogin > windowMinutes;
  }

  // Method to reset specific user for testing
  static Future<void> resetUserForTesting(String userId) async {
    final prefs = await SharedPreferences.getInstance();

    // Clear all greeting-related data for this user
    await prefs.remove('user_has_logged_in_$userId');
    await prefs.remove('user_last_login_$userId');
    await prefs.remove('user_last_login_timestamp_$userId');
    await prefs.remove('user_login_count_$userId');

    print('GreetingHelper: Reset all data for user $userId');
  }

  // Method to reset all users for testing
  static Future<void> resetAllUsersForTesting() async {
    final prefs = await SharedPreferences.getInstance();

    // Get all keys and remove user-related ones
    final Set<String> keys = prefs.getKeys();
    for (String key in keys) {
      if (key.startsWith('user_has_logged_in_') ||
          key.startsWith('user_last_login_') ||
          key.startsWith('user_last_login_timestamp_') ||
          key.startsWith('user_login_count_')) {
        await prefs.remove(key);
      }
    }

    print('GreetingHelper: Reset all user data for testing');
  }

  // Method to force repeat greeting for specific user
  static Future<void> forceRepeatGreetingForUser(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // Mark user as having logged in before
    await prefs.setBool('user_has_logged_in_$userId', true);

    // Set timestamp to 1 hour ago (making it not a new session)
    final oneHourAgo = now.subtract(Duration(hours: 1));
    await prefs.setInt(
        'user_last_login_timestamp_$userId', oneHourAgo.millisecondsSinceEpoch);

    print('GreetingHelper: Forced repeat greeting for user $userId');
  }

  // Utility method to check user login history
  static Future<Map<String, dynamic>> getUserLoginInfo(String userId) async {
    final prefs = await SharedPreferences.getInstance();

    final bool hasLoggedIn =
        prefs.getBool('user_has_logged_in_$userId') ?? false;
    final int? lastLoginTimestamp = prefs.getInt('user_last_login_$userId');
    final int loginCount = prefs.getInt('user_login_count_$userId') ?? 0;

    return {
      'hasLoggedIn': hasLoggedIn,
      'lastLoginTimestamp': lastLoginTimestamp,
      'lastLoginDate': lastLoginTimestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(lastLoginTimestamp).toString()
          : null,
      'loginCount': loginCount,
      'isFirstTimeUser': !hasLoggedIn,
    };
  }

  // Debug method to print all user data for testing
  static Future<void> debugPrintAllUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final Set<String> keys = prefs.getKeys();
    
    print('=== GreetingHelper Debug - All User Data ===');
    for (String key in keys) {
      if (key.startsWith('user_')) {
        final value = prefs.get(key);
        print('  $key: $value');
      }
    }
    print('=== End Debug Data ===');
  }

  // Test method to simulate the greeting flow
  static Future<void> testGreetingFlow(String userId) async {
    print('\n🧪 TESTING GREETING FLOW FOR USER: $userId');
    
    // First, check current state
    print('\n1. Current user state:');
    await debugPrintAllUserData();
    
    final userInfo = await getUserLoginInfo(userId);
    print('   User info: $userInfo');
    
    // Test what greeting would be shown (without marking login)
    final prefs = await SharedPreferences.getInstance();
    final bool isFirstTime = await _isFirstTimeUser(prefs, userId);
    print('\n2. Is first time user: $isFirstTime');
    print('   Expected greeting type: ${isFirstTime ? 'FIRST' : 'REPEAT'}');
    
    print('\n🧪 Test complete for user: $userId\n');
  }
}
import 'dart:convert';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nsl/l10n/app_localizations.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/providers/books_provider.dart';
import 'package:nsl/screens/tab/my_business_records_portrait.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/models/books/books_model.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WebTransactionRecords extends StatefulWidget {
  final String? jsonDataPath;

  const WebTransactionRecords({
    super.key,
    this.jsonDataPath = 'assets/data/collections.json',
  });

  @override
  State<WebTransactionRecords> createState() => _WebTransactionRecordsState();
}

class _WebTransactionRecordsState extends State<WebTransactionRecords> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  bool _isLoading = true;
  List<BooksModel> _collections = [];
  List<BooksModel> _filteredCollections = [];

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Pagination
  static int itemsPerPage = 10;
  int _currentCollectionPage = 1;
  int _totalCollectionPages = 1;

  // Sample banner images - using a placeholder image for now
  final List<String> bannerImages = [
    'assets/images/my_business/collections/my_business_carousel_one.jpg',
    'assets/images/my_business/collections/my_business_carousel_two.jpg',
    'assets/images/my_business/collections/my_business_carousel_three.jpg',
  ];
  final CarouselSliderController carouselController =
      CarouselSliderController();
  bool isHovered = false;

  @override
  void initState() {
    super.initState();
    // Clear asset cache before loading data
    rootBundle.evict(widget.jsonDataPath ?? 'assets/data/collections.json');
    _loadCollectionsData();

    // Initialize filtered collections
    _filteredCollections = List.from(_collections);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final prefs = await SharedPreferences.getInstance();

      _currentCollectionPage = prefs.getInt('transaction_collection_page') ?? 1;
    });

    // Initialize pagination

    _updateTotalPages();

    // Add listener to search controller
    _searchController.addListener(_filterCollections);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterCollections);
    _searchController.dispose();
    super.dispose();
  }

  // Filter collections based on search text and update pagination
  void _filterCollections() {
    final searchText = _searchController.text.toLowerCase();

    setState(() {
      if (searchText.isEmpty) {
        _filteredCollections = List.from(_collections);
      } else {
        _filteredCollections = _collections
            .where((book) =>
                (book.bookName?.toLowerCase().contains(searchText) ?? false))
            .toList();
      }

      // Reset to first page when filtering
      _currentCollectionPage = 1;
      _updateTotalPages();
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  // Update total pages based on filtered collections
  void _updateTotalPages() {
    _totalCollectionPages = (_filteredCollections.length / itemsPerPage).ceil();
    if (_totalCollectionPages < 1) _totalCollectionPages = 1;
  }

  // Navigate to previous page
  void _previousPage() {
    if (_currentCollectionPage > 1) {
      setState(() {
        _currentCollectionPage--;
      });
    }
  }

  // Navigate to next page
  void _nextPage() {
    if (_currentCollectionPage < _totalCollectionPages) {
      setState(() {
        _currentCollectionPage++;
      });
    }
  }

  // Get current page items
  List<BooksModel> _getCurrentPageItems() {
    if (_filteredCollections.isEmpty) return [];

    final startIndex = (_currentCollectionPage - 1) * itemsPerPage;
    final endIndex = startIndex + itemsPerPage;

    if (startIndex >= _filteredCollections.length) return [];

    return _filteredCollections.sublist(
        startIndex,
        endIndex > _filteredCollections.length
            ? _filteredCollections.length
            : endIndex);
  }

  /// Load collections data from Books API and fallback to JSON
  Future<void> _loadCollectionsData() async {
    try {
      // First, try to load data from Books API
      // try {
      //   final booksProvider = BooksProvider();
      //   await booksProvider.fetchBooks();

      //   if (booksProvider.books.isNotEmpty) {
      //     setState(() {
      //       _collections = booksProvider.books;
      //       _filteredCollections = List.from(_collections);
      //       _updateTotalPages();
      //       _isLoading = false;
      //     });
      //     return;
      //   }
      // } catch (apiError) {
      //   debugPrint('Error loading from Books API: $apiError');
      //   // Continue to fallback options
      // }

      // Define fallback data in case API and JSON loading fails
      final List<BooksModel> fallbackData = [
        BooksModel(
          bookId: "book_1",
          bookName: "Procurement Management",
          objectiveCount: 5,
          image:
              "assets/images/my_business/collections/collection_procurement_process.png",
        ),
        BooksModel(
          bookId: "book_2",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_organisation_guides.png",
        ),
        BooksModel(
          bookId: "book_3",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_leave_management.png",
        ),
        BooksModel(
          bookId: "book_1",
          bookName: "Procurement Management",
          objectiveCount: 5,
          image:
              "assets/images/my_business/collections/collection_procurement_process.png",
        ),
        BooksModel(
          bookId: "book_2",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_organisation_guides.png",
        ),
        BooksModel(
          bookId: "book_3",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_leave_management.png",
        ),
        BooksModel(
          bookId: "book_1",
          bookName: "Procurement Management",
          objectiveCount: 5,
          image:
              "assets/images/my_business/collections/collection_procurement_process.png",
        ),
        BooksModel(
          bookId: "book_2",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_organisation_guides.png",
        ),
        BooksModel(
          bookId: "book_3",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_leave_management.png",
        ),
        BooksModel(
          bookId: "book_1",
          bookName: "Procurement Management",
          objectiveCount: 5,
          image:
              "assets/images/my_business/collections/collection_procurement_process.png",
        ),
        BooksModel(
          bookId: "book_2",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_organisation_guides.png",
        ),
        BooksModel(
          bookId: "book_3",
          bookName: "Leave Management",
          objectiveCount: 3,
          image:
              "assets/images/my_business/collections/collection_leave_management.png",
        ),
        // More fallback items can be added here if needed
      ];

      setState(() {
        _collections = fallbackData;
        _filteredCollections = List.from(_collections);
        _updateTotalPages();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error in _loadCollectionsData: $e');
      setState(() {
        _collections = [];
        _filteredCollections = [];
        _isLoading = false;
      });
    }
  }

  @override
 Widget build(BuildContext context) {
  return OrientationBuilder(
    builder: (context, orientation) {
      if (orientation == Orientation.landscape) {
        // Your existing web UI for landscape
        return Scaffold(
          backgroundColor: Color(0xffF7F9FB),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
                      child: Column(
                        children: [
                          // Banner Slider
                          SizedBox(height: AppSpacing.xs),
                          MouseRegion(
                            onEnter: (_) {
                              setState(() {
                                isHovered = true;
                              });
                            },
                            onExit: (_) {
                              setState(() {
                                isHovered = false;
                              });
                            },
                            child: Stack(
                              children: [
                                CarouselSlider.builder(
                                  itemCount: bannerImages.length,
                                  itemBuilder: (context, index, realIndex) {
                                    return ClipRRect(
                                      borderRadius: BorderRadius.circular(AppSpacing.sm),
                                      child: Image.asset(
                                        bannerImages[index],
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Container(
                                            width: double.infinity,
                                            height: 180,
                                            color: Colors.grey[200],
                                            child: Center(
                                              child: Icon(
                                                Icons.broken_image,
                                                color: Colors.grey[400],
                                                size: 48,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    );
                                  },
                                  options: CarouselOptions(
                                    height: MediaQuery.of(context).size.height / 3.5,
                                    viewportFraction: 1.0,
                                    autoPlay: true,
                                    autoPlayInterval: Duration(seconds: 5),
                                    onPageChanged: (index, reason) {
                                      setState(() {
                                        _currentPage = index;
                                      });
                                    },
                                  ),
                                  carouselController: carouselController,
                                ),

                                // Left arrow image (only on hover)
                                if (isHovered)
                                  Positioned(
                                    left: 8,
                                    top: 0,
                                    bottom: 0,
                                    child: Center(
                                      child: InkWell(
                                        onTap: () {
                                          carouselController.previousPage(
                                            duration: Duration(milliseconds: 300),
                                            curve: Curves.ease,
                                          );
                                        },
                                        child: CustomImage.asset(
                                          "assets/images/my_business/collections/left_carousel.svg",
                                        ).toWidget(),
                                      ),
                                    ),
                                  ),

                                // Right arrow image (only on hover)
                                if (isHovered)
                                  Positioned(
                                    right: 8,
                                    top: 0,
                                    bottom: 0,
                                    child: Center(
                                      child: InkWell(
                                        onTap: () {
                                          carouselController.nextPage(
                                            duration: Duration(milliseconds: 300),
                                            curve: Curves.ease,
                                          );
                                        },
                                        child: CustomImage.asset(
                                          "assets/images/my_business/collections/right_carousel.svg",
                                        ).toWidget(),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          // Dot Indicator
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: AppSpacing.xxs),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: List.generate(
                                bannerImages.length,
                                (index) => Container(
                                  margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xxs),
                                  width: _currentPage == index ? 20 : 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: _currentPage == index ? Colors.blue : Colors.grey,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // Records title & search icon/bar
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                AppLocalizations.of(context).translate('myBusinessRecords.records'),
                                style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.bodyLarge(context),
                                  fontWeight: FontManager.medium,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                ),
                              ),
                              _showSearchBar
                                  ? SearchBarWidget(
                                      controller: _searchController,
                                      onClose: _toggleSearchBar,
                                    )
                                  : GestureDetector(
                                      onTap: _toggleSearchBar,
                                      child: MouseRegion(
                                        cursor: SystemMouseCursors.click,
                                        child: Container(
                                          margin: const EdgeInsets.only(right: AppSpacing.sm),
                                          height: 36,
                                          child: HoverableSearchIcon(),
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                          SizedBox(height: AppSpacing.xs),

                          // Loading indicator or grid
                          _isLoading
                              ? const Center(child: CircularProgressIndicator())
                              : LayoutBuilder(builder: (context, constraints) {
                                  // Calculate columns based on available width
                                  int crossAxisCount = 1;
                                  // Adjust number of columns based on screen width
                                  if (constraints.maxWidth >= 1800) {
                                    crossAxisCount = 6;
                                    itemsPerPage = 12;
                                  } else if (constraints.maxWidth >= 1500) {
                                    crossAxisCount = 5;
                                    itemsPerPage = 10;
                                  } else if (constraints.maxWidth >= 1100) {
                                    crossAxisCount = 4;
                                    itemsPerPage = 8;
                                  } else if (constraints.maxWidth >= 1000) {
                                    crossAxisCount = 4;
                                    itemsPerPage = 8;
                                  } else if (constraints.maxWidth >= 600) {
                                    crossAxisCount = 4;
                                    itemsPerPage = 8;
                                  }

                                  return GridView.builder(
                                    shrinkWrap: true,
                                    physics: const AlwaysScrollableScrollPhysics(),
                                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: crossAxisCount,
                                      crossAxisSpacing: 20,
                                      mainAxisSpacing: 20,
                                      childAspectRatio: 1.4,
                                    ),
                                    itemCount: _getCurrentPageItems().length,
                                    itemBuilder: (context, index) {
                                      final book = _getCurrentPageItems()[index];
                                      return BookCard(
                                        book: book,
                                        collectionPage: _currentCollectionPage,
                                      );
                                    },
                                  );
                                }),
                          // Pagination controls
                          Container(
                            margin: const EdgeInsets.symmetric(vertical: AppSpacing.md),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // Navigation buttons
                                Row(
                                  children: [
                                    // Previous button
                                    _HoverPaginationButton(
                                      icon: const Icon(Icons.chevron_left, size: 20),
                                      onPressed: _currentCollectionPage > 1 ? () => _previousPage() : null,
                                    ),
                                    const SizedBox(width: 8),
                                    // Next button
                                    _HoverPaginationButton(
                                      icon: const Icon(Icons.chevron_right, size: 20),
                                      onPressed: _currentCollectionPage < _totalCollectionPages
                                          ? () => _nextPage()
                                          : null,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      } else {
        // Portrait mode - use the new tablet portrait UI
        return TabletPortraitRecords(
          jsonDataPath: widget.jsonDataPath,
        );
      }
    },
  );
}}

class BookCard extends StatefulWidget {
  final BooksModel book;
  final int collectionPage;

  const BookCard({
    super.key,
    required this.book,
    this.collectionPage = 1,
  });

  @override
  State<BookCard> createState() => _BookCardState();
}

class _BookCardState extends State<BookCard> {
  bool _isHovered = false;

  final double horizontalPadding = 12.0;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      // onTap: () async {
      //   final provider = Provider.of<WebHomeProvider>(context, listen: false);
      //   provider.currentScreenIndex = ScreenConstants.myBusinessRecords;
      //   final prefs = await SharedPreferences.getInstance();
      //   await prefs.setInt(
      //       'transaction_collection_page', widget.collectionPage);
      // },
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovered = true),
        onExit: (_) => setState(() => _isHovered = false),
        cursor: SystemMouseCursors.click,
        child: Card(
          margin: EdgeInsets.zero,
          elevation: 0,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSpacing.xs),
            side: BorderSide(
              color: _isHovered
                  ? Color(0xff0058FF)
                  : Color(0xffD0D0D0), // Hover color change
              width: _isHovered ? 1 : 0.5, // Thicker border for hover
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.sm),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  flex: 4,
                  child: Container(
                    child: widget.book.image != null
                        ? CustomImage.asset(widget.book.image!)
                            .toWidget(fit: BoxFit.cover)
                        : CustomImage.asset(
                            "assets/images/my_business/collections/collection_procurement_process.png",
                          ).toWidget(fit: BoxFit.cover),
                  ),
                ),
                SizedBox(
                  height: AppSpacing.sm,
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.topLeft,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LayoutBuilder(
                          builder: (context, constraints) {
                            final screenWidth =
                                MediaQuery.of(context).size.width;
                         final fontSize = screenWidth > 1750
                          ? FontManager.s16  
                          : screenWidth > 1065
                              ? FontManager.s14      
                              : (screenWidth >= 860 && screenWidth <= 960)
                                  ? FontManager.s10  
                                  : FontManager.s12; 

                            return Flexible(
                              child: Text(
                                widget.book.bookName ?? 'Unknown Book',
                                 style: FontManager.getCustomStyle(
                                  fontSize: ResponsiveFontSizes.titleMedium(context),
                                  fontWeight: FontManager.regular,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  height: 1.2,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.left,
                              ),
                            );
                          },
                        ),
                        SizedBox(height: 4),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search icon
          // Padding(
          //   padding: const EdgeInsets.only(left: 8.0),
          //   child: Icon(Icons.search, size: 18, color: Colors.grey.shade600),
          // ),

          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                    color: Colors.grey.shade500,
                    fontSize: FontManager.s14,
                    fontFamily: FontManager.fontFamilyTiemposText),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: FontManager.getCustomStyle(fontSize: FontManager.s14),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_business/search_collection.svg',
        width: 20,
        height: 20,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null, // <-- Change color on hover
      ).toWidget(),
    );
  }
}

class _HoverPaginationButton extends StatefulWidget {
  final Icon icon;
  final VoidCallback? onPressed;

  const _HoverPaginationButton({
    required this.icon,
    this.onPressed,
  });

  @override
  State<_HoverPaginationButton> createState() => _HoverPaginationButtonState();
}

class _HoverPaginationButtonState extends State<_HoverPaginationButton> {
  bool isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => isHovered = true),
      onExit: (_) => setState(() => isHovered = false),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          border: Border.all(
            color: widget.onPressed == null
                ? Colors.grey.shade200
                : (isHovered ? Color(0xff0058FF) : Colors.grey.shade300),
            width: 1.0,
          ),
          // No border radius when hovered
          borderRadius: isHovered ? BorderRadius.zero : null,
          color: Colors.white,
        ),
        child: IconButton(
          icon: widget.icon,
          onPressed: widget.onPressed,
          padding: EdgeInsets.zero,
          color: widget.onPressed == null
              ? Colors.grey.shade400
              : (isHovered ? Color(0xff0058FF) : Colors.black),
          constraints: const BoxConstraints(),
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
      ),
    );
  }
}
